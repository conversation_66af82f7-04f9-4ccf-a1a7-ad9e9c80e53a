[global]
server.socket_host = "************"
server.socket_port = 8081

[/]
tools.sessions.on = True
tools.staticdir.root = os.path.abspath(os.getcwd())

[/static]
tools.staticdir.on = True
tools.staticdir.dir = "./public"

[/data]
tools.staticdir.on = True
tools.staticdir.dir = "./data"


[/temp]
tools.staticdir.on = True
tools.staticdir.dir = "./temp"


[/views]
tools.staticdir.on = True,
tools.staticdir.dir = "./views"
auth.require = []

[/assets]
tools.staticdir.on = True,
tools.staticdir.dir = "./assets"
